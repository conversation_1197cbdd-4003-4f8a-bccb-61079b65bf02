apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "integrations-svc.fullname" . }}-test-connection"
  labels:
{{ include "integrations-svc.labels" . | indent 4 }}
  annotations:
    "helm.sh/hook": test-success
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args:  ['{{ include "integrations-svc.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never

