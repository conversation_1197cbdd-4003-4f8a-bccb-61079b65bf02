apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: {{ .Values.nameOverride }}
spec:
  parentRefs:
  - group: gateway.networking.k8s.io
    kind: Gateway
    name: agora
    namespace: kube-gateways
  rules:
  - backendRefs:
    - group: ""
      kind: Service
      name: {{ .Values.nameOverride }}
      port: {{ .Values.service.port }}
      weight: 1
    filters:
    - type: URLRewrite
      urlRewrite:
        path:
          replacePrefixMatch: /
          type: ReplacePrefixMatch
    matches:
    - path:
        type: PathPrefix
        value: /sca/{{ .Values.nameOverride }}