{{- if and .Values.job .Values.job.enabled }}
{{- $cn := include "integrations-svc.fullname" . -}}
{{- $cjName := .Values.job.name | default $cn }} # the default configmap name can be overriden with the .name property
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ $cjName }}
  labels:
{{ include "integrations-svc.labels" . | indent 4 }}
spec:
  {{- with .Values.job.parallelism }}
  parallelism: {{ . }}
  {{- end }}
  {{- with .Values.job.completions }}
  completions: {{ . }}
  {{- end }}
  {{- with .Values.job.completionMode }}
  completionMode: {{ . }}
  {{- end }}
  {{- with .Values.job.backoffLimit }}
  backoffLimit: {{ . }}
  {{- end }}
  {{- with .Values.job.activeDeadlineSeconds }}
  activeDeadlineSeconds: {{ . }}
  {{- end }}
  {{- with .Values.job.ttlSecondsAfterFinished }}
  ttlSecondsAfterFinished: {{ . }}
  {{- end }}
  {{- with .Values.job.suspend }}
  suspend: {{ . }}
  {{- end }}
  {{- with .Values.job.labelSelector }}
  selector:
  {{ toYaml . | indent 4 }}
  {{- end }}
  {{- with .Values.job.manualSelector }}
  manualSelector: {{ . }}
  {{- end }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ $cjName }}
        app.kubernetes.io/managed-by: {{ .Release.Service }}
      {{- with .Values.job.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      {{- with .Values.job.restartPolicy }}
      restartPolicy: "{{ . }}"
      {{- end }}
      {{- if .Values.job.imagePullSecrets | default .Values.imagePullSecrets }}
      imagePullSecrets:
      {{- range ( .Values.job.imagePullSecrets | default .Values.imagePullSecrets ) }}
        - name: {{ . }}
      {{- end }}
      {{- end }}
      containers:
        - name: {{ include "integrations-svc.name" . }}
          image: {{ .Values.image }}
          imagePullPolicy: {{ .Values.imageConfig.pullPolicy }}
          {{- with .Values.job.command }} # Command to be executed instead of the default entrypoint script (will replace helm variables)
          command: 
            {{- toYaml . | nindent 12 }}
          {{- end }}   
          {{- with .Values.job.args }} # Adding arguments for the entrypoint script
          args:
            {{- toYaml . | nindent 12 }}
          {{- end }}    
          {{- if .Values.env }}
          env:          
          {{- range $key, $val := .Values.env }}
            - name: {{ $key }}
              value: {{ $val | quote }}
          {{- end }}
          {{- end }}
          {{- if .Values.valueFrom }}
          env:
            {{- toYaml .Values.valueFrom | nindent 12 }}
          {{- end }}                    
          resources:
            {{- toYaml ( .Values.job.resources | default .Values.resources ) | indent 12 }}
    {{- with ( .Values.job.nodeSelector | default .Values.nodeSelector ) }}
      nodeSelector:
        {{- toYaml . | indent 8 }}
    {{- end }}
    {{- with ( .Values.job.affinity | default .Values.affinity ) }}
      affinity:
        {{ toYaml . | indent 8 }}
    {{- end }}
    {{- with ( .Values.job.tolerations | default .Values.tolerations ) }}
      tolerations:
{{ toYaml . | indent 8 }}:
    {{- end }}
{{- end }}
