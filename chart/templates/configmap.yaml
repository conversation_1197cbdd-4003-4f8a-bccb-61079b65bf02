{{- if .Values.config_map }}
{{- range .Values.config_map }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ .name }}
  {{- if .labels }}
  labels:
    {{- toYaml .labels | nindent 4 }}
  {{- end }}
  {{- if .annotations }}
  annotations:
    {{- toYaml .annotations | nindent 4 }}
  {{- end }}
data:
  {{- toYaml .data | nindent 4 }}
binaryData:
  {{- toYaml .binaryData | nindent 4 }}
---
  {{- end }}
  {{- end }}