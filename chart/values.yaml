
replicaCount: 1

imageConfig:
  pullPolicy: IfNotPresent

initContainerConfig: {}

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""
hostname: ""

service:
  type: ClusterIP
  port: 80

automountServiceAccountToken: true

ingress:
  enabled: false

  tls: []

alb_ingress:
  enabled: false

virtualService:
  enabled: false

internalIngress: {}
resources: {}
nodeSelector: {}

tolerations: []

affinity: {}

cronjob:
  enabled: false

job:
  enabled: false

## Allow definition of pvc
persistence:
  ##
  ## enable persistance storage
  enabled: false

  ## Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  # storageClass: default
  accessMode: ReadWriteOnce
  ##
  ## Persistant storage size request
  size: 1Gi


# Passing env as a string is a trick allowing to use variables inside the config file. This way the interpolation takes place at runtime

# env: |
#   - name: port
#     value: {{ .Values.service.port }}
#   - name: someint
#     value: 1


#Support for readinessProbe and livenessProbe

# readinessProbe: |
#   failureThreshold: 3
#   httpGet:
#     path: /actuator/health
#     port: {{ .Values.service.port }}
#     scheme: HTTP
#   initialDelaySeconds: 30
#   periodSeconds: 10
#   successThreshold: 1
#   timeoutSeconds: 1

# Define volume mounts and volumes to be used in the deployment

# volumeMounts:
# - mountPath: /run/secrets/env
#   name: delta-secrets
#   readOnly: true
#
# volumes:
# - name: delta-secrets
#   secret:
#     defaultMode: 420
#     secretName: delta-secrets



# support for adding multiple ports. If not defined it will default to the http port

# ports: |
#   - containerPort: 8080
#     name: http
#     protocol: TCP
#   - containerPort: 8888
#     name: jmx
#     protocol: TCP





nameOverride: registry-frontend
fullnameOverride: registry-frontend
image:
  registry: "205744758777.dkr.ecr.us-east-1.amazonaws.com"
  name: "veracode/sca-registry-frontend"
  tag: "ATL-3808-5c36e493"
replicaCount: 1
service: {}
ports: |
  - containerPort: 80
    name: http
    protocol: TCP
env: 
  CLOUDFRONT_HOST: https://registry-frontend-__ENV_BRANCH__.__ENV_NAME__.srcclr.io
  DOWNLOAD_HOST: https://download-__ENV_BRANCH__.__ENV_NAME__.srcclr.io
  CSP_REPORT_URI: https://sentry.__ENV_NAME__.srcclr.io/api/21/csp-report/?sentry_key=66fe2ea766e84017b4ddc51024571d32
  CSP_REPORT_ONLY_URI: https://sentry.__ENV_NAME__.srcclr.io/api/20/csp-report/?sentry_key=8ca43def466246008dc34ef25972dba5
valueFrom:
  - name: ENV_JS
    value: |
      window.SRCCLR_ENV = {
        API_URL: 'https://api-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_API_URL: 'https://ui-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io/srcclr',
        SEARCH2_API_URL: "https://registry-search.stage.srcclr.io",
        BEARER_DOMAINS: ['__ENV_NAME__.srcclr.io', 'localhost'],
        VERACODE_DOMAINS: ['sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io'],
        VERACODE_UIGATEWAY_HOST: 'https://ui-agora-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        VERACODE_LOGIN_HOST: 'https://analysiscenter-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        WWW_URL: 'https://www-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_WWW_URL: 'https://www.veracode.com',
        APP_URL: 'https://app-__ENV_BRANCH__.__ENV_NAME__.srcclr.io',
        VC_APP_URL: 'https://sca-__ENV_BRANCH__.__ENV_NAME__.veracode.io',
        VC_INFO_URL: 'https://info.veracode.com',
        HELP_URL: 'https://help.veracode.com',
        RELEASE_ID: '0',
        ENVIRONMENT: 'QA',
        SENTRY_URL: 'https://498c007fde154a7490af62cdcab732e2@sentry.__ENV_NAME__.srcclr.io/17',
        PLATFORM_FRONTEND_DOMAIN: ['__ENV_NAME__.srcclr.io','local.srcclr.io'],
        ENABLED_FEATURES: ['cvss3'],
        COLLECTOR_COOKIE_DOMAIN: '.srcclr.io',
        COLLECTOR_HOST: 'partlycloudy.ksp.ops2.srcclr.io'
      }
  - name: PLATFORM_HOST
    value: api-__ENV_BRANCH__.__ENV_NAME__.srcclr.io
readinessProbe: |
  failureThreshold: 3
  httpGet:
    path: "/"
    port: 80
    scheme: HTTP
  periodSeconds: 10
  successThreshold: 1
  timeoutSeconds: 1
resources:
  limits:
    memory: 768M
  requests:
    memory: 768M
